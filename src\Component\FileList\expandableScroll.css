.ul-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(100, 100, 100, 0.5) transparent;
}

.ul-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  transition: width 0.3s ease;
}

.ul-scrollbar.scrollbar-expanded::-webkit-scrollbar {
  width: 16px !important;
  height: 16px !important;
}

/* 为了更明显的效果，在展开时改变滚动条颜色 */
.ul-scrollbar.scrollbar-expanded::-webkit-scrollbar-thumb {
  background-color: rgba(255, 0, 0, 0.7) !important; /* 红色，更明显 */
}

/* 滚动条轨道 */
.ul-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

/* 滚动条thumb */
.ul-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(100, 100, 100, 0.5);
  border-radius: 10px;
  border: 2px solid transparent;
  background-clip: content-box;
  transition: background-color 0.3s ease;
}

/* 暗色模式下的滚动条样式 */
.dark .ul-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(200, 200, 200, 0.3);
}

/* 滚动条悬停样式 */
.ul-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(100, 100, 100, 0.8);
}

.dark .ul-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(200, 200, 200, 0.5);
}
