import React, { useEffect } from "react";
import { CtrlMenu } from "../CtrlMenu";
import SettingsIcon from "../../Icons/SettingIcon";
import { WebviewWindow } from "@tauri-apps/api/webviewWindow";
import { useTranslation } from "react-i18next";
import { useSelector, useDispatch } from "react-redux";
import { selectAIMode, toggleAIMode } from "../../slices/aiModeSlice";
import { Switch } from "@mui/material";
import SmartToyIcon from "@mui/icons-material/SmartToy";
import OpenInNewIcon from "@mui/icons-material/OpenInNew";
import { selectPluginInfo } from "../../slices/pluginInfoSlice";
import {
  selectRightPanelExpanded,
  toggleRightPanel,
} from "../../slices/rightPanelSlice";
import VerticalSplitIcon from "@mui/icons-material/VerticalSplit";
import { getCurrentWindow } from "@tauri-apps/api/window";
import {
  applyWindowAcrylic,
  clearWindowAcrylic,
  getAsyncKeyState,
  getSwapButtonState,
} from "../../api/aiverything";
import { setWindowVibrancy } from "../../slices/windowVibrancySlice";
import { platform, version } from "@tauri-apps/plugin-os";

const openSettings = async (title: string) => {
  // Try to get existing settings window
  WebviewWindow.getByLabel("settings")
    .then((window) => {
      window.unminimize();
      window.setFocus();
    })
    .catch((e) => {
      WebviewWindow.getByLabel("main").then((window) => {
        window.hide();
      });
      // Create new window if none exists
      const webview = new WebviewWindow("settings", {
        url: "settings.html",
        width: 800,
        height: 600,
        center: true,
        title: title,
      });

      webview.once("tauri://created", function () {
        // webview window successfully created
      });
      webview.once("tauri://error", function (e) {
        console.error("Error creating settings window:", e);
      });
    });
};

interface FooterProps {
  isCtrlPressed?: boolean;
  inputValue?: string;
  loading?: boolean;
  taskDuration?: number | null;
  totalResultCount?: number;
  selectedFile?: any;
}

export const Footer: React.FC<FooterProps> = ({
  isCtrlPressed,
  inputValue,
  loading,
  taskDuration,
  totalResultCount,
  selectedFile,
}) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const aiMode = useSelector(selectAIMode);
  const currentPluginInfo = useSelector(selectPluginInfo);
  const isRightPanelExpanded = useSelector(selectRightPanelExpanded);

  // 防抖函数
  const debounce = (func: () => void, delay: number) => {
    let timeout: NodeJS.Timeout;
    return () => {
      clearTimeout(timeout);
      timeout = setTimeout(func, delay);
    };
  };

  useEffect(() => {
    const appWindow = getCurrentWindow();
    // 监听窗口移动事件
    const unlisten = appWindow.onMoved(restoreWindowVibrancy);
    return () => {
      unlisten.then((f) => f());
    };
  }, []);

  const restoreWindowVibrancy = debounce(() => {
    applyWindowAcrylic().then(() => {
      dispatch(setWindowVibrancy(true));
    });
  }, 500);

  // 添加检查 Windows 版本的函数
  const hasAcrylicPerformanceIssueVersion = () => {
    const plat = platform();
    const ver = version();

    if (plat !== "windows") return false;

    // 解析版本号
    const [major, minor, build] = ver.split(".").map(Number);

    // https://learn.microsoft.com/en-us/windows/release-health/windows11-release-information
    // Windows 10 v1903+ (build 18362+) 到 Windows 10 最后一个版本
    const isWin10WithIssue =
      major === 10 && minor === 0 && build >= 18362 && build < 22000;

    // Windows 11 build 22000
    const isWin11WithIssue = major === 10 && minor === 0 && build === 22000;

    return isWin10WithIssue || isWin11WithIssue;
  };

  // 修改拖拽处理函数
  const handleDrag = async (e: React.MouseEvent) => {
    if (e.button === 0) {
      const hasPerformanceIssue = hasAcrylicPerformanceIssueVersion();
      if (hasPerformanceIssue) {
        dispatch(setWindowVibrancy(false));
        await clearWindowAcrylic();
      }

      const appWindow = getCurrentWindow();
      await appWindow.startDragging();

      if (hasPerformanceIssue) {
        // 每100ms检查一次鼠标左键状态，如果为放开状态，则恢复窗口模糊
        const interval = setInterval(() => {
          getSwapButtonState().then((state) => {
            if (state) {
              getAsyncKeyState(0x02).then((mouseState) => {
                if (mouseState >= 0) {
                  restoreWindowVibrancy();
                  // 停止检查
                  clearInterval(interval);
                }
              });
            } else {
              getAsyncKeyState(0x01).then((mouseState) => {
                if (mouseState >= 0) {
                  restoreWindowVibrancy();
                  // 停止检查
                  clearInterval(interval);
                }
              });
            }
          });
        }, 100);
      }
    }
  };

  const getHintText = () => {
    if (selectedFile) {
      return `Aiverything: ${t("searchBar.openFile")}`;
    }
    return "Aiverything";
  };

  const hintText = getHintText();

  const handleOpenEntryPage = () => {
    if (currentPluginInfo?.entryPage) {
      const entryPageTitle = `entryPage-${currentPluginInfo.identifier}`;
      WebviewWindow.getByLabel(entryPageTitle)
        .then((window) => {
          window.unminimize();
          window.setFocus();
        })
        .catch(() => {
          WebviewWindow.getByLabel("main").then((window) => {
            window.hide();
          });
          const webview = new WebviewWindow(entryPageTitle, {
            url: currentPluginInfo.entryPage,
            width: 800,
            height: 600,
            center: true,
            title: currentPluginInfo.name,
          });

          webview.once("tauri://created", function () {
            // webview window successfully created
          });
          webview.once("tauri://error", function (e) {
            console.error("Error creating entry page window:", e);
          });
        });
    }
  };

  return (
    <div
      onMouseDown={handleDrag}
      className={`h-6 flex px-2 justify-between items-center cursor-move ${
        inputValue && !loading
          ? "border-t border-gray-300 dark:border-gray-700"
          : ""
      }`}
      style={{
        background: "transparent",
        borderRadius: "0 0 8px 8px",
        overflow: "visible",
        marginTop: "0",
        paddingTop: "3px",
        paddingBottom: "3px",
      }}
    >
      <div className="flex items-center">
        {currentPluginInfo ? (
          <button
            onClick={handleOpenEntryPage}
            className="flex items-center gap-1 text-xs text-blue-600 hover:text-blue-800 transition-colors"
            disabled={!currentPluginInfo?.entryPage}
          >
            <OpenInNewIcon sx={{ fontSize: 16 }} />
            <span>{t("pluginSettings.openEntryPage")}</span>
          </button>
        ) : (
          <div className="relative flex items-center">
            <div className="flex items-center gap-2">
              {selectedFile && (
                <div className="relative ml-1">
                  <CtrlMenu isVisible={isCtrlPressed && selectedFile} />
                  <div
                    className={`px-1.5 py-0.5 text-xs font-medium rounded border shadow-sm transition-all duration-200 ${
                      isCtrlPressed && selectedFile
                        ? "bg-blue-500 text-white border-blue-600 scale-110"
                        : selectedFile
                        ? "bg-gray-100 text-gray-700 border-gray-200 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600"
                        : "bg-gray-50 text-gray-400 border-gray-100 dark:bg-gray-800 dark:text-gray-500 dark:border-gray-700"
                    } min-w-[32px] flex items-center justify-center`}
                  >
                    Ctrl
                  </div>
                </div>
              )}
              {/* 分屏图标按钮 */}
              {inputValue && selectedFile && !currentPluginInfo && (
                <div className="relative group">
                  <button
                    onClick={() => dispatch(toggleRightPanel())}
                    className={`p-0.5 rounded transition-all duration-200 ${
                      isRightPanelExpanded
                        ? "bg-blue-500 text-white hover:bg-blue-600"
                        : "text-gray-500 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700"
                    }`}
                    style={{
                      width: "20px",
                      height: "20px",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                  >
                    <VerticalSplitIcon sx={{ fontSize: 16 }} />
                  </button>
                  {/* Tooltip */}
                  <div className="absolute pointer-events-none bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-50">
                    {isRightPanelExpanded
                      ? t("searchBar.hideRightPanel")
                      : t("searchBar.showRightPanel")}
                  </div>
                </div>
              )}
              <span
                className={`text-xs text-gray-500 ${
                  selectedFile ? "" : "ml-1"
                }`}
              >
                {hintText}
              </span>
            </div>
          </div>
        )}
        <div className="relative group ml-2">
          <span className="w-4 h-4 flex items-center justify-center text-xs font-bold text-gray-500 cursor-help border border-gray-500 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 dark:text-gray-400 dark:border-gray-400">
            i
          </span>
          <div className="absolute pointer-events-none bottom-full left-0 mb-2 p-2 bg-gray-800 text-white text-xs rounded shadow-lg opacity-0 hover:opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-10">
            {t("searchBar.footerHint")}
          </div>
        </div>
      </div>
      <div className="flex items-center gap-2">
        {totalResultCount !== undefined && totalResultCount > 0 && (
          <span className="text-xs text-gray-500 dark:text-gray-400">
            {totalResultCount}{" "}
            {totalResultCount === 1
              ? t("searchBar.result")
              : t("searchBar.results")}
          </span>
        )}
        {taskDuration !== null && taskDuration !== undefined && (
          <span className="text-xs text-gray-500 dark:text-gray-400">
            {taskDuration}ms
          </span>
        )}
        {!currentPluginInfo && (
          <div className="flex items-center">
            <SmartToyIcon
              fontSize="small"
              className={aiMode ? "text-blue-500" : "text-gray-400"}
            />
            <Switch
              size="small"
              checked={aiMode}
              onChange={() => dispatch(toggleAIMode())}
              title={t("searchBar.aiMode")}
            />
          </div>
        )}
        <button
          className="text-xs p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
          onClick={() => {
            openSettings(t("settings.title"));
            WebviewWindow.getByLabel("main").then((window) => {
              window.hide();
            });
          }}
          style={{ margin: "0 3px" }}
        >
          <SettingsIcon className="w-4 h-4 transition-transform duration-300 hover:rotate-90" />
        </button>
      </div>
    </div>
  );
};
